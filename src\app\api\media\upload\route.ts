/**
 * Simple Media Upload API
 * 
 * A straightforward API for uploading files to your custom media library.
 * No complex sync logic - just upload and store.
 */

import { NextRequest, NextResponse } from 'next/server'
import { SimpleMediaLibrary } from '@/lib/simpleMediaLibrary'

const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
const ALLOWED_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/gif',
  'image/webp',
  'video/mp4',
  'video/webm',
  'application/pdf'
]

export async function POST(request: NextRequest) {
  try {
    console.log('[Media Upload] Processing upload request...')

    const formData = await request.formData()
    const file = formData.get('file') as File
    const folder = formData.get('folder') as string
    const tags = formData.get('tags') ? JSON.parse(formData.get('tags') as string) : []

    // Validate file
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: 'File too large. Maximum size is 10MB.' },
        { status: 400 }
      )
    }

    if (!ALLOWED_TYPES.includes(file.type)) {
      return NextResponse.json(
        { error: 'File type not allowed' },
        { status: 400 }
      )
    }

    // Upload using our simple media library
    const result = await SimpleMediaLibrary.upload(file, {
      folder,
      tags
    })

    console.log('[Media Upload] Upload successful:', result.public_id)

    return NextResponse.json({
      success: true,
      data: result,
      message: 'File uploaded successfully'
    })

  } catch (error) {
    console.error('[Media Upload] Upload failed:', error)
    
    return NextResponse.json(
      { 
        error: 'Upload failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    success: true,
    config: {
      maxFileSize: MAX_FILE_SIZE,
      allowedTypes: ALLOWED_TYPES,
      endpoint: '/api/media/upload'
    }
  })
}
