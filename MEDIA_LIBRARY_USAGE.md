# Custom Media Library - Usage Guide

## Overview
Your Custom Media Library provides a centralized interface to manage all media files. When you upload, view, or delete files through this interface, it automatically manages both Cloudinary storage and your local database.

## Key Features

### ✅ Upload Files
- Drag and drop files or click to browse
- Automatically uploads to Cloudinary
- Saves metadata to your database
- Supports images, videos, and PDFs

### ✅ View Media Library
- Grid view of all uploaded media
- Search functionality
- File details (size, format, dimensions)
- Preview images and videos

### ✅ Delete Files (FIXED)
- Click the trash icon on any media item
- **Deletes from BOTH Cloudinary AND database**
- Removes file completely from your system
- No orphaned records left behind

### ✅ Cleanup Orphaned Records
- "Cleanup Orphaned" button removes database records for files that no longer exist in Cloudinary
- Useful if files were deleted directly from Cloudinary console

## How to Use

### 1. Upload Media
```tsx
import SimpleMediaUpload from '@/components/SimpleMediaUpload'

<SimpleMediaUpload
  onUploadSuccess={(file) => console.log('Uploaded:', file)}
  folder="your-folder"
  tags={['tag1', 'tag2']}
/>
```

### 2. Display Media Library
```tsx
import SimpleMediaLibrary from '@/components/SimpleMediaLibrary'

<SimpleMediaLibrary
  onSelectMedia={(media) => console.log('Selected:', media)}
  selectable={true}
/>
```

### 3. API Endpoints

#### Upload File
```bash
POST /api/media/upload
Content-Type: multipart/form-data
Body: { file: File, folder?: string, tags?: string[] }
```

#### Get Media Items
```bash
GET /api/media?limit=50&offset=0
GET /api/media?q=search-term
```

#### Delete Media Item
```bash
DELETE /api/media?public_id=your-file-public-id
```

#### Cleanup Orphaned Records
```bash
PUT /api/media
Body: { "action": "cleanup" }
```

## Webhook Configuration

Set your Cloudinary webhook URL to:
```
https://your-domain.com/api/webhooks/cloudinary
```

This ensures automatic sync when files are deleted from Cloudinary console.

## Troubleshooting

### Problem: Files still show in library after deleting from Cloudinary
**Solution**: Click "Cleanup Orphaned" button to remove database records for deleted files.

### Problem: Delete button doesn't work
**Check**: 
1. Console logs for error messages
2. Network tab for API response
3. Cloudinary credentials are correct

### Problem: Upload works but delete doesn't
**Check**: 
1. Cloudinary API credentials have delete permissions
2. Database connection is working
3. Check server logs for detailed error messages

## Technical Details

### Delete Process
1. **Step 1**: Delete file from Cloudinary using `cloudinary.uploader.destroy()`
2. **Step 2**: Soft delete record in database (sets `deleted_at` timestamp)
3. **Step 3**: Remove from UI immediately
4. **Result**: File completely removed from your system

### Error Handling
- If Cloudinary delete fails but database succeeds: File removed from UI
- If database delete fails but Cloudinary succeeds: Error logged for manual cleanup
- All operations are logged for debugging

### Performance
- Database queries are optimized with indexes
- UI updates immediately without waiting for server confirmation
- Batch operations for cleanup tasks

## Support

If you encounter issues:
1. Check browser console for error messages
2. Check server logs for detailed error information
3. Verify Cloudinary credentials and permissions
4. Test with a simple file upload/delete cycle
