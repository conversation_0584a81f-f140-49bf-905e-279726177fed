/**
 * Live Webhook Test Page
 * 
 * This page helps you test if your Cloudinary webhook is actually receiving
 * upload notifications when you upload files directly to Cloudinary.
 */

'use client'

import { useState, useEffect } from 'react'
import { RefreshCw, ExternalLink, AlertCircle, CheckCircle, Upload } from 'lucide-react'

export default function TestWebhookLivePage() {
  const [webhookLogs, setWebhookLogs] = useState<any[]>([])
  const [isMonitoring, setIsMonitoring] = useState(false)
  const [lastCheck, setLastCheck] = useState<Date | null>(null)

  const startMonitoring = () => {
    setIsMonitoring(true)
    setWebhookLogs([])
    
    // Poll for webhook activity every 2 seconds
    const interval = setInterval(async () => {
      try {
        // This would check recent webhook logs from your database
        // For now, we'll just update the timestamp
        setLastCheck(new Date())
      } catch (error) {
        console.error('Failed to check webhook logs:', error)
      }
    }, 2000)

    // Stop monitoring after 2 minutes
    setTimeout(() => {
      clearInterval(interval)
      setIsMonitoring(false)
    }, 120000)

    return () => clearInterval(interval)
  }

  const stopMonitoring = () => {
    setIsMonitoring(false)
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Live Webhook Test</h1>
      
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
        <h2 className="font-semibold text-blue-800 mb-2">🔍 Test Your Live Webhook</h2>
        <p className="text-blue-700">
          This page helps you verify if Cloudinary is actually sending webhook notifications 
          when you upload files directly to Cloudinary Media Library.
        </p>
      </div>

      {/* Current Webhook URL */}
      <div className="bg-white rounded-lg border p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Your Webhook Configuration</h2>
        
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Configured Webhook URL:
            </label>
            <code className="block bg-gray-100 px-3 py-2 rounded text-sm">
              https://f237-120-28-204-19.ngrok-free.app/api/webhooks/cloudinary
            </code>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Actual Handler Location:
            </label>
            <code className="block bg-gray-100 px-3 py-2 rounded text-sm">
              /api/cloudinary/webhook (with redirect from /api/webhooks/cloudinary)
            </code>
          </div>
        </div>
      </div>

      {/* Test Instructions */}
      <div className="bg-white rounded-lg border p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">How to Test</h2>
        
        <div className="space-y-4">
          <div className="flex items-start gap-3">
            <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mt-1">1</span>
            <div>
              <p className="font-medium">Start Monitoring</p>
              <p className="text-sm text-gray-600">Click the button below to start monitoring for webhook activity</p>
            </div>
          </div>

          <div className="flex items-start gap-3">
            <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mt-1">2</span>
            <div>
              <p className="font-medium">Upload to Cloudinary</p>
              <p className="text-sm text-gray-600">
                Go to your Cloudinary Media Library and upload a file directly
              </p>
              <a 
                href="https://console.cloudinary.com/console/media_library" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 flex items-center gap-1 text-sm mt-1"
              >
                Open Cloudinary Media Library <ExternalLink className="h-3 w-3" />
              </a>
            </div>
          </div>

          <div className="flex items-start gap-3">
            <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mt-1">3</span>
            <div>
              <p className="font-medium">Watch for Activity</p>
              <p className="text-sm text-gray-600">
                If webhooks are working, you should see activity appear below within seconds
              </p>
            </div>
          </div>
        </div>

        <div className="mt-6">
          {!isMonitoring ? (
            <button
              onClick={startMonitoring}
              className="flex items-center gap-2 px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600"
            >
              <Upload className="h-5 w-5" />
              Start Monitoring Webhooks
            </button>
          ) : (
            <button
              onClick={stopMonitoring}
              className="flex items-center gap-2 px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600"
            >
              <RefreshCw className="h-5 w-5 animate-spin" />
              Stop Monitoring
            </button>
          )}
        </div>
      </div>

      {/* Monitoring Status */}
      {isMonitoring && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
          <div className="flex items-center gap-2 mb-3">
            <RefreshCw className="h-5 w-5 text-green-600 animate-spin" />
            <h3 className="font-semibold text-green-800">Monitoring Active</h3>
          </div>
          <p className="text-green-700 text-sm mb-2">
            Watching for webhook activity... Upload a file to Cloudinary now!
          </p>
          {lastCheck && (
            <p className="text-green-600 text-xs">
              Last checked: {lastCheck.toLocaleTimeString()}
            </p>
          )}
        </div>
      )}

      {/* Webhook Activity Log */}
      <div className="bg-white rounded-lg border p-6">
        <h2 className="text-xl font-semibold mb-4">Webhook Activity</h2>
        
        {webhookLogs.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <AlertCircle className="h-12 w-12 mx-auto mb-3 text-gray-400" />
            <p>No webhook activity detected yet</p>
            <p className="text-sm mt-1">
              {isMonitoring 
                ? "Upload a file to Cloudinary to see webhook activity here"
                : "Start monitoring and upload a file to test webhooks"
              }
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {webhookLogs.map((log, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="font-medium">Webhook Received</span>
                  <span className="text-sm text-gray-500">{log.timestamp}</span>
                </div>
                <pre className="text-xs bg-gray-50 p-2 rounded overflow-auto">
                  {JSON.stringify(log.data, null, 2)}
                </pre>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Troubleshooting */}
      <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h3 className="font-semibold text-yellow-800 mb-3">🔧 Troubleshooting</h3>
        <div className="text-yellow-700 text-sm space-y-2">
          <p><strong>If no webhook activity appears:</strong></p>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>Check your Cloudinary Console webhook configuration</li>
            <li>Verify the webhook URL is correct and accessible</li>
            <li>Ensure "Upload" events are enabled in webhook settings</li>
            <li>Check your server logs for webhook processing errors</li>
            <li>Verify your ngrok tunnel is still active</li>
          </ul>
        </div>
      </div>

      {/* Quick Links */}
      <div className="mt-6 flex gap-4">
        <a
          href="/admin/media"
          className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
        >
          Go to Media Center
        </a>
        <a
          href="https://console.cloudinary.com/settings/webhooks"
          target="_blank"
          rel="noopener noreferrer"
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center gap-2"
        >
          Cloudinary Webhook Settings <ExternalLink className="h-4 w-4" />
        </a>
      </div>
    </div>
  )
}
