/**
 * Simple Media Upload Component
 * 
 * A clean, simple upload component for your custom media library.
 * No complex widgets - just drag, drop, and upload.
 */

'use client'

import { useState, useRef } from 'react'
import { Upload, X, Check, AlertCircle } from 'lucide-react'

interface UploadedFile {
  id: string
  public_id: string
  secure_url: string
  original_filename: string
  file_size: number
  format: string
}

interface SimpleMediaUploadProps {
  onUploadSuccess?: (file: UploadedFile) => void
  onUploadError?: (error: string) => void
  folder?: string
  tags?: string[]
  accept?: string
  maxSize?: number
  className?: string
}

export default function SimpleMediaUpload({
  onUploadSuccess,
  onUploadError,
  folder = 'media',
  tags = [],
  accept = 'image/*,video/*,application/pdf',
  maxSize = 10 * 1024 * 1024, // 10MB
  className = ''
}: SimpleMediaUploadProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [uploadMessage, setUploadMessage] = useState('')
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
    
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileUpload(files[0])
    }
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileUpload(files[0])
    }
  }

  const handleFileUpload = async (file: File) => {
    // Validate file size
    if (file.size > maxSize) {
      const error = `File too large. Maximum size is ${Math.round(maxSize / 1024 / 1024)}MB.`
      setUploadStatus('error')
      setUploadMessage(error)
      onUploadError?.(error)
      return
    }

    setIsUploading(true)
    setUploadStatus('idle')
    setUploadMessage('')

    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('folder', folder)
      formData.append('tags', JSON.stringify(tags))

      const response = await fetch('/api/media/upload', {
        method: 'POST',
        body: formData
      })

      const result = await response.json()

      if (result.success) {
        setUploadStatus('success')
        setUploadMessage(`${file.name} uploaded successfully!`)
        onUploadSuccess?.(result.data)
      } else {
        throw new Error(result.error || 'Upload failed')
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed'
      setUploadStatus('error')
      setUploadMessage(errorMessage)
      onUploadError?.(errorMessage)
    } finally {
      setIsUploading(false)
      // Clear status after 3 seconds
      setTimeout(() => {
        setUploadStatus('idle')
        setUploadMessage('')
      }, 3000)
    }
  }

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className={`w-full ${className}`}>
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-8 text-center cursor-pointer
          transition-all duration-200 ease-in-out
          ${isDragging 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
          }
          ${isUploading ? 'pointer-events-none opacity-50' : ''}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={openFileDialog}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          onChange={handleFileSelect}
          className="hidden"
        />

        <div className="flex flex-col items-center space-y-4">
          {isUploading ? (
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          ) : (
            <Upload className="h-12 w-12 text-gray-400" />
          )}

          <div>
            <p className="text-lg font-medium text-gray-900">
              {isUploading ? 'Uploading...' : 'Drop files here or click to upload'}
            </p>
            <p className="text-sm text-gray-500 mt-1">
              Supports images, videos, and PDFs up to {Math.round(maxSize / 1024 / 1024)}MB
            </p>
          </div>
        </div>

        {/* Status Message */}
        {uploadMessage && (
          <div className={`
            absolute bottom-4 left-4 right-4 p-3 rounded-md flex items-center space-x-2
            ${uploadStatus === 'success' ? 'bg-green-50 text-green-800' : ''}
            ${uploadStatus === 'error' ? 'bg-red-50 text-red-800' : ''}
          `}>
            {uploadStatus === 'success' && <Check className="h-4 w-4" />}
            {uploadStatus === 'error' && <AlertCircle className="h-4 w-4" />}
            <span className="text-sm">{uploadMessage}</span>
          </div>
        )}
      </div>
    </div>
  )
}
