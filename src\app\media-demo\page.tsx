/**
 * Demo page showing how to use your Simple Media Library
 */

'use client'

import { useState } from 'react'
import SimpleMediaUpload from '@/components/SimpleMediaUpload'
import SimpleMediaLibrary from '@/components/SimpleMediaLibrary'

export default function MediaDemoPage() {
  const [refreshKey, setRefreshKey] = useState(0)

  const handleUploadSuccess = (file: any) => {
    console.log('File uploaded:', file)
    // Refresh the media library
    setRefreshKey(prev => prev + 1)
  }

  const handleUploadError = (error: string) => {
    console.error('Upload error:', error)
    alert(`Upload failed: ${error}`)
  }

  const handleSelectMedia = (media: any) => {
    console.log('Selected media:', media)
    alert(`Selected: ${media.original_filename}`)
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Simple Media Library Demo</h1>
      
      {/* Upload Section */}
      <div className="mb-12">
        <h2 className="text-xl font-semibold mb-4">Upload Files</h2>
        <SimpleMediaUpload
          onUploadSuccess={handleUploadSuccess}
          onUploadError={handleUploadError}
          folder="demo"
          tags={['demo', 'test']}
          className="max-w-2xl"
        />
      </div>

      {/* Media Library Section */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Your Media Library</h2>
        <SimpleMediaLibrary
          key={refreshKey}
          onSelectMedia={handleSelectMedia}
          selectable={true}
        />
      </div>
    </div>
  )
}
