/**
 * Demo page showing how to use your Simple Media Library
 */

'use client'

import { useState } from 'react'
import SimpleMediaUpload from '@/components/SimpleMediaUpload'
import SimpleMediaLibrary from '@/components/SimpleMediaLibrary'

export default function MediaDemoPage() {
  const [refreshKey, setRefreshKey] = useState(0)
  const [syncStatus, setSyncStatus] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)

  const handleUploadSuccess = (file: any) => {
    console.log('File uploaded:', file)
    // Refresh the media library
    setRefreshKey(prev => prev + 1)
  }

  const handleUploadError = (error: string) => {
    console.error('Upload error:', error)
    alert(`Upload failed: ${error}`)
  }

  const handleSelectMedia = (media: any) => {
    console.log('Selected media:', media)
    alert(`Selected: ${media.original_filename}`)
  }

  const handleSync = async (action: string) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/media/sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action })
      })

      const result = await response.json()

      if (result.success) {
        setSyncStatus(result)
        alert(result.message)
        if (action === 'cleanup' || action === 'full-sync') {
          setRefreshKey(prev => prev + 1)
        }
      } else {
        alert('Sync failed: ' + result.error)
      }
    } catch (error) {
      console.error('Sync error:', error)
      alert('Sync failed: ' + (error instanceof Error ? error.message : 'Unknown error'))
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Professional Media Library</h1>

      {/* Sync Controls */}
      <div className="mb-8 p-6 bg-gray-50 rounded-lg">
        <h2 className="text-lg font-semibold mb-4">Sync Controls</h2>
        <div className="flex gap-4 flex-wrap">
          <button
            onClick={() => handleSync('verify')}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            Verify Sync Status
          </button>
          <button
            onClick={() => handleSync('cleanup')}
            disabled={isLoading}
            className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50"
          >
            Cleanup Orphaned Records
          </button>
          <button
            onClick={() => handleSync('full-sync')}
            disabled={isLoading}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            Full Sync from Cloudinary
          </button>
        </div>

        {syncStatus && (
          <div className="mt-4 p-4 bg-white rounded border">
            <h3 className="font-medium">Last Sync Result:</h3>
            <pre className="text-sm text-gray-600 mt-2">
              {JSON.stringify(syncStatus, null, 2)}
            </pre>
          </div>
        )}
      </div>

      {/* Upload Section */}
      <div className="mb-12">
        <h2 className="text-xl font-semibold mb-4">Upload Files</h2>
        <SimpleMediaUpload
          onUploadSuccess={handleUploadSuccess}
          onUploadError={handleUploadError}
          folder="demo"
          tags={['demo', 'test']}
          className="max-w-2xl"
        />
      </div>

      {/* Media Library Section */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Your Media Library</h2>
        <SimpleMediaLibrary
          key={refreshKey}
          onSelectMedia={handleSelectMedia}
          selectable={true}
        />
      </div>
    </div>
  )
}
