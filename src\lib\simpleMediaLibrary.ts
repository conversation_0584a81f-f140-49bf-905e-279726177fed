/**
 * Simple Custom Media Library
 * 
 * A straightforward media library that uploads to Cloudinary and stores metadata locally.
 * No complex bidirectional sync - just simple, reliable media management.
 */

import { uploadToCloudinary, CLOUDINARY_FOLDERS } from '@/lib/cloudinary'
import { createClient } from '@/utils/supabase/server'

export interface SimpleMediaItem {
  id: string
  public_id: string
  secure_url: string
  original_filename: string
  file_size: number
  format: string
  resource_type: string
  width?: number
  height?: number
  created_at: string
  folder?: string
  tags?: string[]
}

export interface UploadOptions {
  folder?: string
  tags?: string[]
  public_id?: string
}

export class SimpleMediaLibrary {
  /**
   * Upload a file and save to our media library
   */
  static async upload(file: File, options: UploadOptions = {}): Promise<SimpleMediaItem> {
    try {
      console.log('[SimpleMediaLibrary] Uploading file:', file.name)

      // Upload to Cloudinary
      const result = await uploadToCloudinary(file, {
        folder: options.folder || CLOUDINARY_FOLDERS.MEDIA,
        tags: ['lgu-project', ...(options.tags || [])],
        public_id: options.public_id,
        resource_type: 'auto'
      })

      // Save metadata to our database
      const supabase = await createClient()
      const { data: { user } } = await supabase.auth.getUser()

      const mediaItem = {
        cloudinary_public_id: result.public_id,
        cloudinary_version: result.version,
        cloudinary_signature: result.signature,
        original_filename: file.name,
        display_name: file.name,
        file_size: result.bytes,
        mime_type: file.type,
        format: result.format,
        width: result.width,
        height: result.height,
        folder: result.folder,
        tags: result.tags,
        secure_url: result.secure_url,
        url: result.url,
        resource_type: result.resource_type,
        access_mode: 'public',
        sync_status: 'synced',
        uploaded_by: user?.id || null
      }

      const { data, error } = await supabase
        .from('media_assets')
        .insert(mediaItem)
        .select()
        .single()

      if (error) {
        console.error('[SimpleMediaLibrary] Database save failed:', error)
        // Still return the Cloudinary result even if DB save fails
      }

      return {
        id: data?.id || result.public_id,
        public_id: result.public_id,
        secure_url: result.secure_url,
        original_filename: file.name,
        file_size: result.bytes,
        format: result.format,
        resource_type: result.resource_type,
        width: result.width,
        height: result.height,
        created_at: result.created_at,
        folder: result.folder,
        tags: result.tags
      }

    } catch (error) {
      console.error('[SimpleMediaLibrary] Upload failed:', error)
      throw new Error(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Get all media items from our library
   */
  static async getAll(limit = 50, offset = 0): Promise<SimpleMediaItem[]> {
    try {
      const supabase = await createClient()
      
      const { data, error } = await supabase
        .from('media_assets')
        .select('*')
        .is('deleted_at', null)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1)

      if (error) {
        console.error('[SimpleMediaLibrary] Fetch failed:', error)
        return []
      }

      return data.map(item => ({
        id: item.id,
        public_id: item.cloudinary_public_id,
        secure_url: item.secure_url,
        original_filename: item.original_filename,
        file_size: item.file_size,
        format: item.format,
        resource_type: item.resource_type,
        width: item.width,
        height: item.height,
        created_at: item.created_at,
        folder: item.folder,
        tags: item.tags
      }))

    } catch (error) {
      console.error('[SimpleMediaLibrary] Get all failed:', error)
      return []
    }
  }

  /**
   * Search media items
   */
  static async search(query: string, limit = 20): Promise<SimpleMediaItem[]> {
    try {
      const supabase = await createClient()
      
      const { data, error } = await supabase
        .from('media_assets')
        .select('*')
        .is('deleted_at', null)
        .or(`original_filename.ilike.%${query}%,display_name.ilike.%${query}%`)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) {
        console.error('[SimpleMediaLibrary] Search failed:', error)
        return []
      }

      return data.map(item => ({
        id: item.id,
        public_id: item.cloudinary_public_id,
        secure_url: item.secure_url,
        original_filename: item.original_filename,
        file_size: item.file_size,
        format: item.format,
        resource_type: item.resource_type,
        width: item.width,
        height: item.height,
        created_at: item.created_at,
        folder: item.folder,
        tags: item.tags
      }))

    } catch (error) {
      console.error('[SimpleMediaLibrary] Search failed:', error)
      return []
    }
  }

  /**
   * Delete a media item (both from Cloudinary and database)
   */
  static async delete(publicId: string): Promise<boolean> {
    try {
      console.log('[SimpleMediaLibrary] Starting delete operation for:', publicId)

      let cloudinaryDeleteSuccess = false
      let databaseDeleteSuccess = false

      // Step 1: Delete from Cloudinary first
      try {
        const { cloudinary } = await import('@/lib/cloudinary')
        if (!cloudinary) {
          throw new Error('Cloudinary not initialized')
        }

        console.log('[SimpleMediaLibrary] Deleting from Cloudinary...')
        const result = await cloudinary.uploader.destroy(publicId, {
          resource_type: 'auto', // This handles images, videos, and raw files
          invalidate: true // Invalidate CDN cache
        })

        console.log('[SimpleMediaLibrary] Cloudinary delete result:', result)

        // Cloudinary returns { result: 'ok' } for successful deletions
        // or { result: 'not found' } if the asset doesn't exist
        if (result.result === 'ok' || result.result === 'not found') {
          cloudinaryDeleteSuccess = true
          console.log('[SimpleMediaLibrary] Cloudinary deletion successful')
        } else {
          throw new Error(`Cloudinary delete failed with result: ${result.result}`)
        }

      } catch (cloudinaryError) {
        console.error('[SimpleMediaLibrary] Cloudinary delete failed:', cloudinaryError)
        // Don't fail the entire operation if Cloudinary delete fails
        // The file might already be deleted or there might be a network issue
        console.warn('[SimpleMediaLibrary] Continuing with database deletion despite Cloudinary error')
      }

      // Step 2: Delete from database
      try {
        console.log('[SimpleMediaLibrary] Deleting from database...')
        const supabase = await createClient()

        // First check if the record exists
        const { data: existingRecord, error: checkError } = await supabase
          .from('media_assets')
          .select('id, cloudinary_public_id')
          .eq('cloudinary_public_id', publicId)
          .is('deleted_at', null)
          .single()

        if (checkError && checkError.code !== 'PGRST116') {
          throw new Error(`Database check failed: ${checkError.message}`)
        }

        if (!existingRecord) {
          console.warn('[SimpleMediaLibrary] Record not found in database or already deleted')
          // If Cloudinary deletion was successful, consider this a success
          return cloudinaryDeleteSuccess
        }

        // Perform soft delete
        const { error: deleteError } = await supabase
          .from('media_assets')
          .update({
            deleted_at: new Date().toISOString(),
            sync_status: 'synced' // Mark as synced since we deleted from both places
          })
          .eq('cloudinary_public_id', publicId)

        if (deleteError) {
          throw new Error(`Database delete failed: ${deleteError.message}`)
        }

        databaseDeleteSuccess = true
        console.log('[SimpleMediaLibrary] Database deletion successful')

      } catch (databaseError) {
        console.error('[SimpleMediaLibrary] Database delete failed:', databaseError)
        // If Cloudinary deletion succeeded but database failed, we have an inconsistency
        if (cloudinaryDeleteSuccess) {
          console.error('[SimpleMediaLibrary] CRITICAL: Cloudinary deleted but database update failed - manual cleanup needed')
        }
        return false
      }

      // Step 3: Evaluate overall success
      const overallSuccess = cloudinaryDeleteSuccess || databaseDeleteSuccess

      if (overallSuccess) {
        console.log('[SimpleMediaLibrary] Delete operation completed successfully')
      } else {
        console.error('[SimpleMediaLibrary] Delete operation failed completely')
      }

      return overallSuccess

    } catch (error) {
      console.error('[SimpleMediaLibrary] Delete operation failed with exception:', error)
      return false
    }
  }

  /**
   * Sync database with Cloudinary - remove orphaned records
   */
  static async cleanupOrphanedRecords(): Promise<{
    cleaned: number
    errors: string[]
  }> {
    try {
      console.log('[SimpleMediaLibrary] Starting cleanup of orphaned records...')

      const supabase = await createClient()
      const { cloudinary } = await import('@/lib/cloudinary')

      if (!cloudinary) {
        throw new Error('Cloudinary not available')
      }

      // Get all non-deleted records from database
      const { data: dbAssets, error } = await supabase
        .from('media_assets')
        .select('id, cloudinary_public_id')
        .is('deleted_at', null)

      if (error) {
        throw new Error(`Database query failed: ${error.message}`)
      }

      let cleaned = 0
      const errors: string[] = []

      // Check each asset in Cloudinary
      for (const asset of dbAssets || []) {
        try {
          // Try to get the asset from Cloudinary
          await cloudinary.api.resource(asset.cloudinary_public_id)
          // If we get here, the asset exists in Cloudinary
        } catch (cloudinaryError: any) {
          // If asset doesn't exist in Cloudinary (404), mark as deleted in database
          if (cloudinaryError.http_code === 404) {
            console.log(`[SimpleMediaLibrary] Orphaned record found: ${asset.cloudinary_public_id}`)

            const { error: deleteError } = await supabase
              .from('media_assets')
              .update({
                deleted_at: new Date().toISOString(),
                sync_status: 'synced'
              })
              .eq('id', asset.id)

            if (deleteError) {
              errors.push(`Failed to cleanup ${asset.cloudinary_public_id}: ${deleteError.message}`)
            } else {
              cleaned++
            }
          } else {
            errors.push(`Error checking ${asset.cloudinary_public_id}: ${cloudinaryError.message}`)
          }
        }
      }

      console.log(`[SimpleMediaLibrary] Cleanup completed. Cleaned: ${cleaned}, Errors: ${errors.length}`)

      return { cleaned, errors }

    } catch (error) {
      console.error('[SimpleMediaLibrary] Cleanup failed:', error)
      return {
        cleaned: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      }
    }
  }
}
