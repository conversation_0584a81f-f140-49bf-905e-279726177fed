/**
 * Simple Custom Media Library
 * 
 * A straightforward media library that uploads to Cloudinary and stores metadata locally.
 * No complex bidirectional sync - just simple, reliable media management.
 */

import { uploadToCloudinary, CLOUDINARY_FOLDERS } from '@/lib/cloudinary'
import { createClient } from '@/utils/supabase/server'

export interface SimpleMediaItem {
  id: string
  public_id: string
  secure_url: string
  original_filename: string
  file_size: number
  format: string
  resource_type: string
  width?: number
  height?: number
  created_at: string
  folder?: string
  tags?: string[]
}

export interface UploadOptions {
  folder?: string
  tags?: string[]
  public_id?: string
}

export class SimpleMediaLibrary {
  /**
   * Upload a file and save to our media library
   */
  static async upload(file: File, options: UploadOptions = {}): Promise<SimpleMediaItem> {
    try {
      console.log('[SimpleMediaLibrary] Uploading file:', file.name)

      // Upload to Cloudinary
      const result = await uploadToCloudinary(file, {
        folder: options.folder || CLOUDINARY_FOLDERS.MEDIA,
        tags: ['lgu-project', ...(options.tags || [])],
        public_id: options.public_id,
        resource_type: 'auto'
      })

      // Save metadata to our database
      const supabase = await createClient()
      const { data: { user } } = await supabase.auth.getUser()

      const mediaItem = {
        cloudinary_public_id: result.public_id,
        cloudinary_version: result.version,
        cloudinary_signature: result.signature,
        original_filename: file.name,
        display_name: file.name,
        file_size: result.bytes,
        mime_type: file.type,
        format: result.format,
        width: result.width,
        height: result.height,
        folder: result.folder,
        tags: result.tags,
        secure_url: result.secure_url,
        url: result.url,
        resource_type: result.resource_type,
        access_mode: 'public',
        sync_status: 'synced',
        uploaded_by: user?.id || null
      }

      const { data, error } = await supabase
        .from('media_assets')
        .insert(mediaItem)
        .select()
        .single()

      if (error) {
        console.error('[SimpleMediaLibrary] Database save failed:', error)
        // Still return the Cloudinary result even if DB save fails
      }

      return {
        id: data?.id || result.public_id,
        public_id: result.public_id,
        secure_url: result.secure_url,
        original_filename: file.name,
        file_size: result.bytes,
        format: result.format,
        resource_type: result.resource_type,
        width: result.width,
        height: result.height,
        created_at: result.created_at,
        folder: result.folder,
        tags: result.tags
      }

    } catch (error) {
      console.error('[SimpleMediaLibrary] Upload failed:', error)
      throw new Error(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Get all media items from our library
   */
  static async getAll(limit = 50, offset = 0): Promise<SimpleMediaItem[]> {
    try {
      const supabase = await createClient()
      
      const { data, error } = await supabase
        .from('media_assets')
        .select('*')
        .is('deleted_at', null)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1)

      if (error) {
        console.error('[SimpleMediaLibrary] Fetch failed:', error)
        return []
      }

      return data.map(item => ({
        id: item.id,
        public_id: item.cloudinary_public_id,
        secure_url: item.secure_url,
        original_filename: item.original_filename,
        file_size: item.file_size,
        format: item.format,
        resource_type: item.resource_type,
        width: item.width,
        height: item.height,
        created_at: item.created_at,
        folder: item.folder,
        tags: item.tags
      }))

    } catch (error) {
      console.error('[SimpleMediaLibrary] Get all failed:', error)
      return []
    }
  }

  /**
   * Search media items
   */
  static async search(query: string, limit = 20): Promise<SimpleMediaItem[]> {
    try {
      const supabase = await createClient()
      
      const { data, error } = await supabase
        .from('media_assets')
        .select('*')
        .is('deleted_at', null)
        .or(`original_filename.ilike.%${query}%,display_name.ilike.%${query}%`)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) {
        console.error('[SimpleMediaLibrary] Search failed:', error)
        return []
      }

      return data.map(item => ({
        id: item.id,
        public_id: item.cloudinary_public_id,
        secure_url: item.secure_url,
        original_filename: item.original_filename,
        file_size: item.file_size,
        format: item.format,
        resource_type: item.resource_type,
        width: item.width,
        height: item.height,
        created_at: item.created_at,
        folder: item.folder,
        tags: item.tags
      }))

    } catch (error) {
      console.error('[SimpleMediaLibrary] Search failed:', error)
      return []
    }
  }

  /**
   * Delete a media item
   */
  static async delete(publicId: string): Promise<boolean> {
    try {
      const supabase = await createClient()
      
      // Soft delete in database
      const { error } = await supabase
        .from('media_assets')
        .update({ deleted_at: new Date().toISOString() })
        .eq('cloudinary_public_id', publicId)

      if (error) {
        console.error('[SimpleMediaLibrary] Delete failed:', error)
        return false
      }

      return true

    } catch (error) {
      console.error('[SimpleMediaLibrary] Delete failed:', error)
      return false
    }
  }
}
