-- Fix Supabase Real-time DELETE events for media_assets table
-- Run this in your Supabase SQL Editor

-- Step 1: Check if media_assets is in the realtime publication
SELECT schemaname, tablename 
FROM pg_publication_tables 
WHERE pubname = 'supabase_realtime' 
AND tablename = 'media_assets';

-- Step 2: Add media_assets table to realtime publication if not already added
ALTER PUBLICATION supabase_realtime ADD TABLE media_assets;

-- Step 3: Enable realtime for the table (if not already enabled)
ALTER TABLE media_assets REPLICA IDENTITY FULL;

-- Step 4: Grant necessary permissions for realtime
GRANT SELECT, INSERT, UPDATE, DELETE ON media_assets TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON media_assets TO anon;

-- Step 5: Verify the publication includes media_assets
SELECT schemaname, tablename 
FROM pg_publication_tables 
WHERE pubname = 'supabase_realtime';
