/**
 * Delete Test Page
 * 
 * Professional testing interface for media deletion functionality.
 * Tests both Cloudinary and database deletion to ensure consistency.
 */

'use client'

import { useState } from 'react'
import { Trash2, Upload, RefreshCw, CheckCircle, XCircle, AlertTriangle } from 'lucide-react'

interface TestResult {
  step: string
  status: 'pending' | 'success' | 'error' | 'warning'
  message: string
  details?: any
}

export default function DeleteTestPage() {
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [testPublicId, setTestPublicId] = useState('')

  const addResult = (result: TestResult) => {
    setTestResults(prev => [...prev, result])
  }

  const clearResults = () => {
    setTestResults([])
  }

  const runDeleteTest = async (publicId: string) => {
    if (!publicId.trim()) {
      alert('Please enter a public_id to test')
      return
    }

    setIsRunning(true)
    clearResults()

    try {
      addResult({
        step: 'Initialize',
        status: 'pending',
        message: `Starting delete test for: ${publicId}`
      })

      // Step 1: Check if asset exists in database before deletion
      addResult({
        step: 'Pre-check Database',
        status: 'pending',
        message: 'Checking if asset exists in database...'
      })

      const preCheckResponse = await fetch(`/api/media?q=${encodeURIComponent(publicId)}`)
      const preCheckData = await preCheckResponse.json()
      
      const existsInDb = preCheckData.success && preCheckData.data.some((item: any) => item.public_id === publicId)
      
      addResult({
        step: 'Pre-check Database',
        status: existsInDb ? 'success' : 'warning',
        message: existsInDb ? 'Asset found in database' : 'Asset not found in database',
        details: { found: existsInDb, count: preCheckData.data?.length || 0 }
      })

      // Step 2: Check if asset exists in Cloudinary
      addResult({
        step: 'Pre-check Cloudinary',
        status: 'pending',
        message: 'Checking if asset exists in Cloudinary...'
      })

      // We'll infer this from the delete response since we don't have direct Cloudinary API access here

      // Step 3: Perform the deletion
      addResult({
        step: 'Delete Operation',
        status: 'pending',
        message: 'Executing delete operation...'
      })

      const deleteResponse = await fetch(`/api/media?public_id=${encodeURIComponent(publicId)}`, {
        method: 'DELETE'
      })

      const deleteData = await deleteResponse.json()

      addResult({
        step: 'Delete Operation',
        status: deleteData.success ? 'success' : 'error',
        message: deleteData.message || (deleteData.success ? 'Delete successful' : 'Delete failed'),
        details: deleteData
      })

      // Step 4: Verify deletion in database
      addResult({
        step: 'Post-check Database',
        status: 'pending',
        message: 'Verifying deletion in database...'
      })

      await new Promise(resolve => setTimeout(resolve, 1000)) // Wait a bit for consistency

      const postCheckResponse = await fetch(`/api/media?q=${encodeURIComponent(publicId)}`)
      const postCheckData = await postCheckResponse.json()
      
      const stillExistsInDb = postCheckData.success && postCheckData.data.some((item: any) => item.public_id === publicId)
      
      addResult({
        step: 'Post-check Database',
        status: stillExistsInDb ? 'error' : 'success',
        message: stillExistsInDb ? 'ERROR: Asset still exists in database!' : 'Asset successfully removed from database',
        details: { found: stillExistsInDb, count: postCheckData.data?.length || 0 }
      })

      // Step 5: Summary
      const overallSuccess = deleteData.success && !stillExistsInDb
      
      addResult({
        step: 'Test Summary',
        status: overallSuccess ? 'success' : 'error',
        message: overallSuccess 
          ? '✅ DELETE TEST PASSED: Asset deleted from both Cloudinary and database'
          : '❌ DELETE TEST FAILED: Inconsistency detected',
        details: {
          cloudinary_delete: deleteData.success,
          database_cleanup: !stillExistsInDb,
          overall_success: overallSuccess
        }
      })

    } catch (error) {
      addResult({
        step: 'Test Error',
        status: 'error',
        message: 'Test failed with exception',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setIsRunning(false)
    }
  }

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'pending':
        return <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />
    }
  }

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return 'bg-green-50 border-green-200'
      case 'error':
        return 'bg-red-50 border-red-200'
      case 'warning':
        return 'bg-yellow-50 border-yellow-200'
      case 'pending':
        return 'bg-blue-50 border-blue-200'
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Media Library Delete Test</h1>
      
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8">
        <h2 className="font-semibold text-yellow-800 mb-2">⚠️ Professional Delete Testing</h2>
        <p className="text-yellow-700 text-sm">
          This page tests the complete delete functionality of your Custom Media Library.
          It verifies that files are properly deleted from both Cloudinary and your database.
        </p>
      </div>

      {/* Test Input */}
      <div className="bg-white rounded-lg border p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4">Test Configuration</h2>
        
        <div className="flex gap-4 items-end">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Public ID to Delete
            </label>
            <input
              type="text"
              value={testPublicId}
              onChange={(e) => setTestPublicId(e.target.value)}
              placeholder="Enter the public_id of a file to delete (e.g., demo/test-image)"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isRunning}
            />
          </div>
          
          <button
            onClick={() => runDeleteTest(testPublicId)}
            disabled={isRunning || !testPublicId.trim()}
            className="px-6 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isRunning ? (
              <>
                <RefreshCw className="h-4 w-4 animate-spin" />
                Testing...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4" />
                Test Delete
              </>
            )}
          </button>
        </div>
      </div>

      {/* Test Results */}
      {testResults.length > 0 && (
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Test Results</h2>
            <button
              onClick={clearResults}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              Clear Results
            </button>
          </div>
          
          <div className="space-y-3">
            {testResults.map((result, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border ${getStatusColor(result.status)}`}
              >
                <div className="flex items-start gap-3">
                  {getStatusIcon(result.status)}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium">{result.step}</span>
                      <span className="text-sm text-gray-500">
                        #{index + 1}
                      </span>
                    </div>
                    <p className="text-sm text-gray-700 mb-2">{result.message}</p>
                    {result.details && (
                      <details className="text-xs">
                        <summary className="cursor-pointer text-gray-500 hover:text-gray-700">
                          View Details
                        </summary>
                        <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                          {JSON.stringify(result.details, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 className="font-semibold mb-3">How to Use This Test</h3>
        <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
          <li>First, upload a test file using your media library</li>
          <li>Note the public_id of the uploaded file</li>
          <li>Enter the public_id in the input field above</li>
          <li>Click "Test Delete" to run the comprehensive delete test</li>
          <li>Review the results to ensure both Cloudinary and database deletion worked</li>
        </ol>
        
        <div className="mt-4 p-3 bg-blue-50 rounded border border-blue-200">
          <p className="text-sm text-blue-700">
            <strong>Expected Result:</strong> The test should show successful deletion from both 
            Cloudinary and your database. If any step fails, there's an issue with your delete implementation.
          </p>
        </div>
      </div>
    </div>
  )
}
