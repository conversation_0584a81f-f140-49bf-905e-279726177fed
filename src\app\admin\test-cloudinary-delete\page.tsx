/**
 * Test Cloudinary Delete Functionality
 * 
 * This page helps debug Cloudinary delete issues.
 */

'use client'

import { useState } from 'react'
import { Trash2, TestTube, AlertCircle, CheckCircle } from 'lucide-react'

export default function TestCloudinaryDeletePage() {
  const [publicId, setPublicId] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [testResult, setTestResult] = useState<any>(null)

  const testCloudinaryDelete = async () => {
    if (!publicId.trim()) {
      alert('Please enter a public_id to test')
      return
    }

    setIsLoading(true)
    setTestResult(null)

    try {
      const response = await fetch('/api/test-cloudinary-delete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ public_id: publicId })
      })

      const result = await response.json()
      setTestResult(result)

    } catch (error) {
      setTestResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="h-5 w-5 text-green-500" />
    ) : (
      <AlertCircle className="h-5 w-5 text-red-500" />
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Test Cloudinary Delete</h1>
      
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8">
        <h2 className="font-semibold text-yellow-800 mb-2">🧪 Debug Tool</h2>
        <p className="text-yellow-700 text-sm">
          This tool tests if Cloudinary delete operations are working properly.
          Use this to debug why files aren't being deleted from Cloudinary.
        </p>
      </div>

      {/* Test Input */}
      <div className="bg-white rounded-lg border p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4">Test Configuration</h2>
        
        <div className="flex gap-4 items-end">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Public ID to Test Delete
            </label>
            <input
              type="text"
              value={publicId}
              onChange={(e) => setPublicId(e.target.value)}
              placeholder="Enter the public_id of a file to test delete (e.g., lgu-uploads/media/test-image)"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isLoading}
            />
          </div>
          
          <button
            onClick={testCloudinaryDelete}
            disabled={isLoading || !publicId.trim()}
            className="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Testing...
              </>
            ) : (
              <>
                <TestTube className="h-4 w-4" />
                Test Delete
              </>
            )}
          </button>
        </div>
      </div>

      {/* Test Results */}
      {testResult && (
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center gap-3 mb-4">
            {getStatusIcon(testResult.success)}
            <h2 className="text-xl font-semibold">
              Test Results: {testResult.success ? 'SUCCESS' : 'FAILED'}
            </h2>
          </div>
          
          {testResult.error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded">
              <p className="text-red-800 font-medium">Error:</p>
              <p className="text-red-700 text-sm">{testResult.error}</p>
            </div>
          )}

          {testResult.debug && (
            <div className="space-y-4">
              <h3 className="font-semibold">Debug Information:</h3>
              
              {/* Environment Check */}
              {testResult.debug.env_check && (
                <div className="p-3 bg-gray-50 rounded">
                  <h4 className="font-medium mb-2">Environment Variables:</h4>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(testResult.debug.env_check.cloud_name)}
                      <span>Cloud Name</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(testResult.debug.env_check.api_key)}
                      <span>API Key</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(testResult.debug.env_check.api_secret)}
                      <span>API Secret</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Cloudinary Status */}
              <div className="p-3 bg-gray-50 rounded">
                <h4 className="font-medium mb-2">Cloudinary Status:</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(testResult.debug.cloudinary_available)}
                    <span>Cloudinary Instance Available</span>
                  </div>
                  {testResult.debug.asset_existed_before_delete !== undefined && (
                    <div className="flex items-center gap-2">
                      {getStatusIcon(testResult.debug.asset_existed_before_delete)}
                      <span>Asset Existed Before Delete</span>
                    </div>
                  )}
                  {testResult.debug.deletion_verified !== undefined && (
                    <div className="flex items-center gap-2">
                      {getStatusIcon(testResult.debug.deletion_verified)}
                      <span>Deletion Verified (Asset No Longer Exists)</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Delete Result */}
              {testResult.debug.delete_result && (
                <div className="p-3 bg-gray-50 rounded">
                  <h4 className="font-medium mb-2">Cloudinary Delete Result:</h4>
                  <pre className="text-xs bg-white p-2 rounded border overflow-auto">
                    {JSON.stringify(testResult.debug.delete_result, null, 2)}
                  </pre>
                </div>
              )}

              {/* Full Debug Data */}
              <details className="p-3 bg-gray-50 rounded">
                <summary className="font-medium cursor-pointer">Full Debug Data</summary>
                <pre className="text-xs bg-white p-2 rounded border mt-2 overflow-auto">
                  {JSON.stringify(testResult.debug, null, 2)}
                </pre>
              </details>
            </div>
          )}
        </div>
      )}

      {/* Instructions */}
      <div className="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 className="font-semibold mb-3">How to Use This Test</h3>
        <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
          <li>Upload a test file to your media library first</li>
          <li>Note the public_id of the uploaded file (check console logs or database)</li>
          <li>Enter the public_id in the input field above</li>
          <li>Click "Test Delete" to test Cloudinary deletion</li>
          <li>Review the results to see what's failing</li>
        </ol>
        
        <div className="mt-4 p-3 bg-blue-50 rounded border border-blue-200">
          <p className="text-sm text-blue-700">
            <strong>Expected Result:</strong> The test should show successful deletion from Cloudinary.
            If it fails, check the debug information to see what's wrong with your Cloudinary configuration.
          </p>
        </div>
      </div>
    </div>
  )
}
