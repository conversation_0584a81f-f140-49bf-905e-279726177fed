/**
 * Check Cloudinary Webhook Configuration
 * 
 * This endpoint checks your Cloudinary webhook configuration to see
 * what events are configured and if upload events are included.
 */

import { NextRequest, NextResponse } from 'next/server'
import { cloudinary } from '@/lib/cloudinary'

export async function GET(request: NextRequest) {
  try {
    console.log('[Webhook Check] Checking Cloudinary webhook configuration...')

    // Check if cloudinary instance is available
    if (!cloudinary) {
      return NextResponse.json({
        success: false,
        error: 'Cloudinary not configured',
        message: 'Check your environment variables'
      }, { status: 500 })
    }

    // Try to get webhook configuration using Admin API
    try {
      // Note: This requires Admin API access
      const webhooks = await cloudinary.api.list_upload_presets()
      
      return NextResponse.json({
        success: true,
        message: 'Cloudinary API accessible',
        webhook_diagnosis: {
          api_accessible: true,
          configured_webhook_url: 'https://f237-120-28-204-19.ngrok-free.app/api/webhooks/cloudinary',
          expected_events: ['upload', 'delete', 'update'],
          recommendations: [
            'Verify webhook URL is accessible from internet',
            'Check that upload events are enabled in Cloudinary Console',
            'Ensure signature verification is enabled',
            'Test with a direct upload to Cloudinary Media Library'
          ]
        },
        next_steps: [
          'Go to https://console.cloudinary.com/settings/webhooks',
          'Verify your webhook configuration includes upload events',
          'Test by uploading a file directly to Cloudinary',
          'Check server logs for webhook activity'
        ]
      })

    } catch (apiError: any) {
      console.log('[Webhook Check] Admin API not accessible:', apiError.message)
      
      return NextResponse.json({
        success: true,
        message: 'Manual webhook verification needed',
        webhook_diagnosis: {
          api_accessible: false,
          configured_webhook_url: 'https://f237-120-28-204-19.ngrok-free.app/api/webhooks/cloudinary',
          manual_check_required: true,
          likely_issues: [
            'Webhook not configured for upload events',
            'Webhook URL not accessible from Cloudinary',
            'Signature verification failing',
            'ngrok tunnel expired or changed'
          ]
        },
        manual_verification_steps: [
          {
            step: 1,
            action: 'Check Cloudinary Console',
            url: 'https://console.cloudinary.com/settings/webhooks',
            verify: 'Ensure webhook URL is correct and upload events are enabled'
          },
          {
            step: 2,
            action: 'Test webhook endpoint',
            url: `${request.nextUrl.origin}/api/webhooks/cloudinary`,
            verify: 'Endpoint should be accessible and return webhook info'
          },
          {
            step: 3,
            action: 'Upload test file',
            url: 'https://console.cloudinary.com/console/media_library',
            verify: 'Upload a file and check server logs for webhook activity'
          },
          {
            step: 4,
            action: 'Check ngrok status',
            verify: 'Ensure ngrok tunnel is active and URL matches webhook config'
          }
        ]
      })
    }

  } catch (error) {
    console.error('[Webhook Check] Error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Webhook check failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      troubleshooting: {
        common_issues: [
          'Environment variables not set correctly',
          'Cloudinary API credentials invalid',
          'Network connectivity issues'
        ],
        check_env_vars: [
          'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME',
          'CLOUDINARY_API_KEY', 
          'CLOUDINARY_API_SECRET'
        ]
      }
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { test_webhook_url } = await request.json()
    const webhookUrl = test_webhook_url || 'https://f237-120-28-204-19.ngrok-free.app/api/webhooks/cloudinary'

    console.log('[Webhook Check] Testing webhook URL accessibility:', webhookUrl)

    // Test if the webhook URL is accessible
    try {
      const response = await fetch(webhookUrl, {
        method: 'GET',
        headers: {
          'User-Agent': 'Cloudinary-Webhook-Test'
        }
      })

      const isAccessible = response.ok
      const responseText = await response.text()

      return NextResponse.json({
        success: true,
        webhook_test: {
          url: webhookUrl,
          accessible: isAccessible,
          status_code: response.status,
          response_preview: responseText.substring(0, 200),
          test_timestamp: new Date().toISOString()
        },
        recommendations: isAccessible ? [
          'Webhook URL is accessible ✅',
          'Check Cloudinary Console for event configuration',
          'Verify upload events are enabled',
          'Test with actual file upload'
        ] : [
          'Webhook URL is not accessible ❌',
          'Check if ngrok tunnel is running',
          'Verify the URL in Cloudinary Console',
          'Ensure firewall allows incoming connections'
        ]
      })

    } catch (fetchError) {
      return NextResponse.json({
        success: false,
        webhook_test: {
          url: webhookUrl,
          accessible: false,
          error: fetchError instanceof Error ? fetchError.message : 'Unknown error',
          test_timestamp: new Date().toISOString()
        },
        recommendations: [
          'Webhook URL is not reachable ❌',
          'Check if ngrok is running and URL is correct',
          'Verify network connectivity',
          'Update webhook URL in Cloudinary Console if needed'
        ]
      })
    }

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Webhook test failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
