/**
 * Simple Media Library API
 * 
 * Get and search media items from your custom library.
 */

import { NextRequest, NextResponse } from 'next/server'
import { SimpleMediaLibrary } from '@/lib/simpleMediaLibrary'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    let items
    if (query) {
      console.log('[Media API] Searching for:', query)
      items = await SimpleMediaLibrary.search(query, limit)
    } else {
      console.log('[Media API] Getting all media items')
      items = await SimpleMediaLibrary.getAll(limit, offset)
    }

    return NextResponse.json({
      success: true,
      data: items,
      count: items.length,
      query: query || null,
      pagination: {
        limit,
        offset,
        has_more: items.length === limit
      }
    })

  } catch (error) {
    console.error('[Media API] Fetch failed:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch media items',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  const startTime = Date.now()

  try {
    const { searchParams } = new URL(request.url)
    const publicId = searchParams.get('public_id')

    if (!publicId) {
      console.error('[Media API] Delete request missing public_id parameter')
      return NextResponse.json(
        {
          success: false,
          error: 'Missing public_id parameter',
          message: 'public_id is required for delete operations'
        },
        { status: 400 }
      )
    }

    console.log(`[Media API] Starting delete operation for: ${publicId}`)

    // Validate public_id format (basic validation)
    if (publicId.trim().length === 0) {
      console.error('[Media API] Invalid public_id: empty string')
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid public_id',
          message: 'public_id cannot be empty'
        },
        { status: 400 }
      )
    }

    // Perform the deletion
    const success = await SimpleMediaLibrary.delete(publicId)
    const processingTime = Date.now() - startTime

    if (success) {
      console.log(`[Media API] Delete successful for ${publicId} in ${processingTime}ms`)
      return NextResponse.json({
        success: true,
        message: 'Media item deleted successfully',
        public_id: publicId,
        processing_time_ms: processingTime,
        timestamp: new Date().toISOString()
      })
    } else {
      console.error(`[Media API] Delete failed for ${publicId} after ${processingTime}ms`)
      return NextResponse.json(
        {
          success: false,
          error: 'Delete operation failed',
          message: 'Failed to delete media item from Cloudinary and/or database',
          public_id: publicId,
          processing_time_ms: processingTime,
          timestamp: new Date().toISOString()
        },
        { status: 500 }
      )
    }

  } catch (error) {
    const processingTime = Date.now() - startTime
    console.error('[Media API] Delete operation threw exception:', error)

    return NextResponse.json(
      {
        success: false,
        error: 'Delete operation failed',
        message: 'An unexpected error occurred during deletion',
        details: error instanceof Error ? error.message : 'Unknown error',
        processing_time_ms: processingTime,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { action } = await request.json()

    if (action === 'cleanup') {
      console.log('[Media API] Starting cleanup of orphaned records...')

      const result = await SimpleMediaLibrary.cleanupOrphanedRecords()

      return NextResponse.json({
        success: true,
        message: 'Cleanup completed',
        cleaned: result.cleaned,
        errors: result.errors
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )

  } catch (error) {
    console.error('[Media API] Action failed:', error)

    return NextResponse.json(
      {
        error: 'Action failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
