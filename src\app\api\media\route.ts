/**
 * Simple Media Library API
 * 
 * Get and search media items from your custom library.
 */

import { NextRequest, NextResponse } from 'next/server'
import { SimpleMediaLibrary } from '@/lib/simpleMediaLibrary'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    let items
    if (query) {
      console.log('[Media API] Searching for:', query)
      items = await SimpleMediaLibrary.search(query, limit)
    } else {
      console.log('[Media API] Getting all media items')
      items = await SimpleMediaLibrary.getAll(limit, offset)
    }

    return NextResponse.json({
      success: true,
      data: items,
      count: items.length,
      query: query || null,
      pagination: {
        limit,
        offset,
        has_more: items.length === limit
      }
    })

  } catch (error) {
    console.error('[Media API] Fetch failed:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch media items',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const publicId = searchParams.get('public_id')

    if (!publicId) {
      return NextResponse.json(
        { error: 'Missing public_id parameter' },
        { status: 400 }
      )
    }

    console.log('[Media API] Deleting media item:', publicId)
    const success = await SimpleMediaLibrary.delete(publicId)

    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Media item deleted successfully'
      })
    } else {
      return NextResponse.json(
        { error: 'Failed to delete media item' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('[Media API] Delete failed:', error)

    return NextResponse.json(
      {
        error: 'Delete failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { action } = await request.json()

    if (action === 'cleanup') {
      console.log('[Media API] Starting cleanup of orphaned records...')

      const result = await SimpleMediaLibrary.cleanupOrphanedRecords()

      return NextResponse.json({
        success: true,
        message: 'Cleanup completed',
        cleaned: result.cleaned,
        errors: result.errors
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )

  } catch (error) {
    console.error('[Media API] Action failed:', error)

    return NextResponse.json(
      {
        error: 'Action failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
