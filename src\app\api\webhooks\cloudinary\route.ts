/**
 * Cloudinary Webhook Handler
 * 
 * This creates the correct webhook endpoint that Cloudinary expects.
 * Redirects to the existing webhook handler to maintain compatibility.
 */

import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    console.log('[Webhook Redirect] Redirecting Cloudinary webhook to correct handler...')
    
    // Get the request body and headers
    const body = await request.text()
    const headers = new Headers()
    
    // Copy all headers from the original request
    request.headers.forEach((value, key) => {
      headers.set(key, value)
    })

    // Forward to the actual webhook handler
    const response = await fetch(`${request.nextUrl.origin}/api/cloudinary/webhook`, {
      method: 'POST',
      headers,
      body
    })

    const result = await response.json()
    
    return NextResponse.json(result, { status: response.status })

  } catch (error) {
    console.error('[Webhook Redirect] Failed to forward webhook:', error)
    
    return NextResponse.json(
      { 
        error: 'Webhook forwarding failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Cloudinary webhook endpoint',
    correct_endpoint: `${request.nextUrl.origin}/api/cloudinary/webhook`,
    redirect_active: true
  })
}
