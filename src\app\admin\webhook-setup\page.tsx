/**
 * Cloudinary Webhook Setup Helper
 * 
 * This page helps you configure Cloudinary webhooks properly to ensure
 * files uploaded directly to Cloudinary appear in your Custom Media Library.
 */

'use client'

import { useState, useEffect } from 'react'
import { Settings, CheckCircle, AlertCircle, ExternalLink, Copy, RefreshCw } from 'lucide-react'

export default function WebhookSetupPage() {
  const [webhookUrl, setWebhookUrl] = useState('')
  const [isTestingWebhook, setIsTestingWebhook] = useState(false)
  const [webhookStatus, setWebhookStatus] = useState<any>(null)
  const [copySuccess, setCopySuccess] = useState(false)
  const [isTestingUpload, setIsTestingUpload] = useState(false)
  const [uploadTestResult, setUploadTestResult] = useState<any>(null)

  useEffect(() => {
    // Set the webhook URL based on current domain
    const currentUrl = window.location.origin
    setWebhookUrl(`${currentUrl}/api/cloudinary/webhook`)
  }, [])

  const testWebhook = async () => {
    setIsTestingWebhook(true)
    try {
      const response = await fetch('/api/cloudinary/webhook')
      const data = await response.json()
      setWebhookStatus(data)
    } catch (error) {
      setWebhookStatus({
        success: false,
        error: 'Failed to test webhook endpoint'
      })
    } finally {
      setIsTestingWebhook(false)
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopySuccess(true)
      setTimeout(() => setCopySuccess(false), 2000)
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
    }
  }

  const testWebhookUpload = async () => {
    setIsTestingUpload(true)
    try {
      const response = await fetch('/api/test-webhook', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ test_public_id: `test-upload-${Date.now()}` })
      })
      const data = await response.json()
      setUploadTestResult(data)
    } catch (error) {
      setUploadTestResult({
        success: false,
        error: 'Failed to test webhook upload simulation'
      })
    } finally {
      setIsTestingUpload(false)
    }
  }

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="h-5 w-5 text-green-500" />
    ) : (
      <AlertCircle className="h-5 w-5 text-red-500" />
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Cloudinary Webhook Setup</h1>
      
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
        <h2 className="font-semibold text-blue-800 mb-2">🎯 Goal</h2>
        <p className="text-blue-700">
          Configure Cloudinary webhooks so that files uploaded directly to Cloudinary 
          (outside your Custom Media Library) automatically appear in your media library.
        </p>
      </div>

      {/* Step 1: Webhook URL */}
      <div className="bg-white rounded-lg border p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">1</span>
          Your Webhook URL
        </h2>
        
        <div className="bg-gray-50 rounded-lg p-4 mb-4">
          <div className="flex items-center gap-2 mb-2">
            <code className="flex-1 bg-white px-3 py-2 rounded border text-sm">
              {webhookUrl}
            </code>
            <button
              onClick={() => copyToClipboard(webhookUrl)}
              className="px-3 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 flex items-center gap-1"
            >
              <Copy className="h-4 w-4" />
              {copySuccess ? 'Copied!' : 'Copy'}
            </button>
          </div>
          <p className="text-sm text-gray-600">
            This is your webhook endpoint URL. Copy this for use in Cloudinary Console.
          </p>
        </div>

        <button
          onClick={testWebhook}
          disabled={isTestingWebhook}
          className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          <RefreshCw className={`h-4 w-4 ${isTestingWebhook ? 'animate-spin' : ''}`} />
          {isTestingWebhook ? 'Testing...' : 'Test Webhook Endpoint'}
        </button>

        {webhookStatus && (
          <div className="mt-4 p-3 bg-gray-50 rounded border">
            <div className="flex items-center gap-2 mb-2">
              {getStatusIcon(webhookStatus.success)}
              <span className="font-medium">
                Webhook Endpoint: {webhookStatus.success ? 'Working' : 'Failed'}
              </span>
            </div>
            {webhookStatus.error && (
              <p className="text-red-600 text-sm">{webhookStatus.error}</p>
            )}
          </div>
        )}
      </div>

      {/* Step 2: Cloudinary Console Setup */}
      <div className="bg-white rounded-lg border p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">2</span>
          Configure in Cloudinary Console
        </h2>
        
        <div className="space-y-4">
          <div className="flex items-start gap-3">
            <span className="bg-gray-200 text-gray-700 rounded-full w-6 h-6 flex items-center justify-center text-sm mt-1">a</span>
            <div>
              <p className="font-medium">Go to Cloudinary Console</p>
              <a 
                href="https://console.cloudinary.com/settings/webhooks" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 flex items-center gap-1 text-sm"
              >
                Open Cloudinary Webhook Settings <ExternalLink className="h-3 w-3" />
              </a>
            </div>
          </div>

          <div className="flex items-start gap-3">
            <span className="bg-gray-200 text-gray-700 rounded-full w-6 h-6 flex items-center justify-center text-sm mt-1">b</span>
            <div>
              <p className="font-medium">Add New Webhook</p>
              <p className="text-sm text-gray-600">Click "Add webhook" button</p>
            </div>
          </div>

          <div className="flex items-start gap-3">
            <span className="bg-gray-200 text-gray-700 rounded-full w-6 h-6 flex items-center justify-center text-sm mt-1">c</span>
            <div>
              <p className="font-medium">Configure Webhook</p>
              <div className="mt-2 space-y-2 text-sm">
                <div className="bg-gray-50 p-3 rounded">
                  <strong>Notification URL:</strong>
                  <code className="block mt-1 bg-white px-2 py-1 rounded text-xs">
                    {webhookUrl}
                  </code>
                </div>
                <div className="bg-gray-50 p-3 rounded">
                  <strong>Notification Types:</strong>
                  <ul className="mt-1 space-y-1">
                    <li>✅ <strong>Upload</strong> - When files are uploaded</li>
                    <li>✅ <strong>Delete</strong> - When files are deleted</li>
                    <li>✅ <strong>Update</strong> - When files are updated</li>
                  </ul>
                </div>
                <div className="bg-gray-50 p-3 rounded">
                  <strong>Settings:</strong>
                  <ul className="mt-1 space-y-1">
                    <li>✅ Enable signature verification</li>
                    <li>✅ Set format to JSON</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-start gap-3">
            <span className="bg-gray-200 text-gray-700 rounded-full w-6 h-6 flex items-center justify-center text-sm mt-1">d</span>
            <div>
              <p className="font-medium">Save Configuration</p>
              <p className="text-sm text-gray-600">Click "Save" to activate the webhook</p>
            </div>
          </div>
        </div>
      </div>

      {/* Step 3: Test the Setup */}
      <div className="bg-white rounded-lg border p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">3</span>
          Test the Setup
        </h2>
        
        <div className="space-y-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-medium text-blue-800 mb-2">🧪 Test Webhook Processing</h3>
            <p className="text-blue-700 text-sm mb-3">
              Test if your webhook endpoint can process upload notifications correctly:
            </p>
            <button
              onClick={testWebhookUpload}
              disabled={isTestingUpload}
              className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 ${isTestingUpload ? 'animate-spin' : ''}`} />
              {isTestingUpload ? 'Testing...' : 'Simulate Upload Webhook'}
            </button>

            {uploadTestResult && (
              <div className="mt-3 p-3 bg-white rounded border">
                <div className="flex items-center gap-2 mb-2">
                  {getStatusIcon(uploadTestResult.success)}
                  <span className="font-medium">
                    Webhook Test: {uploadTestResult.success ? 'Success' : 'Failed'}
                  </span>
                </div>
                {uploadTestResult.success ? (
                  <div className="text-sm text-green-700">
                    <p>✅ Webhook processing is working correctly!</p>
                    <p>Check your Media Center to see the test file.</p>
                  </div>
                ) : (
                  <p className="text-red-600 text-sm">{uploadTestResult.error}</p>
                )}
              </div>
            )}
          </div>

          <div className="space-y-3">
            <h3 className="font-medium">Manual Testing Steps:</h3>
            <div className="flex items-start gap-3">
              <span className="bg-gray-200 text-gray-700 rounded-full w-6 h-6 flex items-center justify-center text-sm mt-1">1</span>
              <p>Go to your Cloudinary Media Library and upload a file directly</p>
            </div>
            <div className="flex items-start gap-3">
              <span className="bg-gray-200 text-gray-700 rounded-full w-6 h-6 flex items-center justify-center text-sm mt-1">2</span>
              <p>Check your Custom Media Library - the file should appear automatically</p>
            </div>
            <div className="flex items-start gap-3">
              <span className="bg-gray-200 text-gray-700 rounded-full w-6 h-6 flex items-center justify-center text-sm mt-1">3</span>
              <p>Check your server logs for webhook processing messages</p>
            </div>
          </div>
        </div>
      </div>

      {/* Environment Variables */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h3 className="font-semibold text-yellow-800 mb-3">⚠️ Important Environment Variables</h3>
        <p className="text-yellow-700 mb-3">
          Make sure these environment variables are set in your <code>.env.local</code> file:
        </p>
        <div className="bg-white p-3 rounded border text-sm font-mono">
          <div>CLOUDINARY_API_SECRET=your_api_secret</div>
          <div>NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name</div>
          <div>CLOUDINARY_API_KEY=your_api_key</div>
        </div>
        <p className="text-yellow-700 text-sm mt-2">
          The API secret is required for webhook signature verification.
        </p>
      </div>

      {/* Quick Links */}
      <div className="mt-8 flex gap-4">
        <a
          href="/admin/media"
          className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
        >
          Go to Media Center
        </a>
        <a
          href="https://console.cloudinary.com/settings/webhooks"
          target="_blank"
          rel="noopener noreferrer"
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center gap-2"
        >
          Open Cloudinary Console <ExternalLink className="h-4 w-4" />
        </a>
      </div>
    </div>
  )
}
