/**
 * Test Cloudinary Delete Functionality
 * 
 * This endpoint tests if Cloudinary delete operations are working properly.
 * Use this to debug delete issues.
 */

import { NextRequest, NextResponse } from 'next/server'
import { cloudinary } from '@/lib/cloudinary'

export async function POST(request: NextRequest) {
  try {
    const { public_id } = await request.json()

    if (!public_id) {
      return NextResponse.json({
        success: false,
        error: 'Missing public_id parameter'
      }, { status: 400 })
    }

    console.log('[Test Cloudinary Delete] Testing delete for:', public_id)

    // Test 1: Check if cloudinary instance is available
    if (!cloudinary) {
      return NextResponse.json({
        success: false,
        error: 'Cloudinary instance not available',
        debug: {
          cloudinary_available: false,
          env_check: {
            cloud_name: !!process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
            api_key: !!process.env.CLOUDINARY_API_KEY,
            api_secret: !!process.env.CLOUDINARY_API_SECRET
          }
        }
      })
    }

    // Test 2: Check if the asset exists first
    let assetExists = false
    try {
      const resourceInfo = await cloudinary.api.resource(public_id)
      assetExists = true
      console.log('[Test Cloudinary Delete] Asset exists:', resourceInfo.public_id)
    } catch (checkError: any) {
      console.log('[Test Cloudinary Delete] Asset check result:', checkError.http_code)
      if (checkError.http_code === 404) {
        return NextResponse.json({
          success: false,
          error: 'Asset not found in Cloudinary',
          debug: {
            public_id,
            asset_exists: false,
            check_error: checkError.message
          }
        })
      }
    }

    // Test 3: Attempt to delete
    console.log('[Test Cloudinary Delete] Attempting delete...')
    
    const deleteResult = await cloudinary.uploader.destroy(public_id, {
      resource_type: 'auto',
      invalidate: true
    })

    console.log('[Test Cloudinary Delete] Delete result:', deleteResult)

    // Test 4: Verify deletion
    let deletionVerified = false
    try {
      await cloudinary.api.resource(public_id)
      deletionVerified = false // If we get here, asset still exists
    } catch (verifyError: any) {
      if (verifyError.http_code === 404) {
        deletionVerified = true // Asset not found = successfully deleted
      }
    }

    return NextResponse.json({
      success: deleteResult.result === 'ok',
      debug: {
        public_id,
        cloudinary_available: true,
        asset_existed_before_delete: assetExists,
        delete_result: deleteResult,
        deletion_verified: deletionVerified,
        env_check: {
          cloud_name: !!process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
          api_key: !!process.env.CLOUDINARY_API_KEY,
          api_secret: !!process.env.CLOUDINARY_API_SECRET
        }
      }
    })

  } catch (error) {
    console.error('[Test Cloudinary Delete] Error:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      debug: {
        error_type: error instanceof Error ? error.constructor.name : typeof error,
        error_details: error instanceof Error ? error.stack : String(error)
      }
    }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Cloudinary Delete Test Endpoint',
    usage: 'POST with { "public_id": "your-file-public-id" }',
    purpose: 'Test if Cloudinary delete operations are working'
  })
}
