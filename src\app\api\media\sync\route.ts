/**
 * Media Sync API
 * 
 * Professional sync operations between Cloudinary and Supabase.
 * Handles cleanup, verification, and bidirectional synchronization.
 */

import { NextRequest, NextResponse } from 'next/server'
import { SimpleMediaLibrary } from '@/lib/simpleMediaLibrary'
import { createClient } from '@/utils/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { action, options = {} } = await request.json()

    switch (action) {
      case 'cleanup':
        return await handleCleanup()
      case 'verify':
        return await handleVerification(options)
      case 'full-sync':
        return await handleFullSync(options)
      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: cleanup, verify, or full-sync' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('[Media Sync API] Operation failed:', error)
    
    return NextResponse.json(
      { 
        error: 'Sync operation failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

async function handleCleanup() {
  console.log('[Media Sync API] Starting cleanup operation...')
  
  const result = await SimpleMediaLibrary.cleanupOrphanedRecords()
  
  return NextResponse.json({
    success: true,
    action: 'cleanup',
    cleaned: result.cleaned,
    errors: result.errors,
    message: `Cleanup completed. Removed ${result.cleaned} orphaned records.`
  })
}

async function handleVerification(options: any) {
  console.log('[Media Sync API] Starting verification...')
  
  try {
    const supabase = await createClient()
    const { cloudinary } = await import('@/lib/cloudinary')
    
    if (!cloudinary) {
      throw new Error('Cloudinary not available')
    }

    // Get database stats
    const { data: dbStats, error: dbError } = await supabase
      .from('media_assets')
      .select('count', { count: 'exact' })
      .is('deleted_at', null)

    if (dbError) {
      throw new Error(`Database query failed: ${dbError.message}`)
    }

    // Get Cloudinary stats
    const cloudinaryResult = await cloudinary.api.resources({
      resource_type: 'auto',
      max_results: 1
    })

    const verification = {
      database_count: dbStats?.[0]?.count || 0,
      cloudinary_total: cloudinaryResult.total_count || 0,
      in_sync: false,
      recommendations: []
    }

    verification.in_sync = verification.database_count === verification.cloudinary_total

    if (!verification.in_sync) {
      if (verification.database_count > verification.cloudinary_total) {
        verification.recommendations.push('Run cleanup to remove orphaned database records')
      } else {
        verification.recommendations.push('Run full-sync to import missing Cloudinary assets')
      }
    }

    return NextResponse.json({
      success: true,
      action: 'verify',
      verification,
      message: verification.in_sync ? 'Database and Cloudinary are in sync' : 'Sync issues detected'
    })

  } catch (error) {
    console.error('[Media Sync API] Verification failed:', error)
    throw error
  }
}

async function handleFullSync(options: any) {
  console.log('[Media Sync API] Starting full sync...')
  
  try {
    const { cloudinary } = await import('@/lib/cloudinary')
    const supabase = await createClient()
    
    if (!cloudinary) {
      throw new Error('Cloudinary not available')
    }

    const { limit = 100, folder } = options
    let synced = 0
    let errors: string[] = []
    let next_cursor: string | undefined

    // Get current user for uploaded_by field
    const { data: { user } } = await supabase.auth.getUser()

    do {
      try {
        // Fetch resources from Cloudinary
        const searchOptions: any = {
          resource_type: 'auto',
          max_results: Math.min(limit, 100),
          sort_by: [['created_at', 'desc']]
        }

        if (next_cursor) {
          searchOptions.next_cursor = next_cursor
        }

        if (folder) {
          searchOptions.expression = `folder:${folder}`
        }

        const result = await cloudinary.api.resources(searchOptions)

        // Process each resource
        for (const resource of result.resources) {
          try {
            // Check if already exists in database
            const { data: existing } = await supabase
              .from('media_assets')
              .select('id')
              .eq('cloudinary_public_id', resource.public_id)
              .single()

            if (!existing) {
              // Insert new record
              const mediaItem = {
                cloudinary_public_id: resource.public_id,
                cloudinary_version: resource.version,
                cloudinary_signature: resource.signature || '',
                original_filename: resource.original_filename || resource.public_id,
                display_name: resource.display_name || resource.original_filename || resource.public_id,
                file_size: resource.bytes,
                mime_type: resource.format ? `image/${resource.format}` : 'application/octet-stream',
                format: resource.format,
                width: resource.width,
                height: resource.height,
                folder: resource.folder,
                tags: resource.tags || [],
                secure_url: resource.secure_url,
                url: resource.url,
                resource_type: resource.resource_type,
                access_mode: 'public',
                sync_status: 'synced',
                uploaded_by: user?.id || null
              }

              const { error } = await supabase
                .from('media_assets')
                .insert(mediaItem)

              if (error) {
                errors.push(`Failed to sync ${resource.public_id}: ${error.message}`)
              } else {
                synced++
              }
            }

          } catch (itemError) {
            errors.push(`Error processing ${resource.public_id}: ${itemError}`)
          }
        }

        next_cursor = result.next_cursor

      } catch (batchError) {
        errors.push(`Batch sync failed: ${batchError}`)
        break
      }

    } while (next_cursor && synced < limit)

    return NextResponse.json({
      success: true,
      action: 'full-sync',
      synced,
      errors: errors.slice(0, 10), // Limit error list
      total_errors: errors.length,
      message: `Full sync completed. Synced ${synced} assets with ${errors.length} errors.`
    })

  } catch (error) {
    console.error('[Media Sync API] Full sync failed:', error)
    throw error
  }
}

export async function GET() {
  return NextResponse.json({
    success: true,
    available_actions: [
      {
        action: 'cleanup',
        description: 'Remove database records for files that no longer exist in Cloudinary',
        method: 'POST',
        body: { action: 'cleanup' }
      },
      {
        action: 'verify',
        description: 'Check sync status between database and Cloudinary',
        method: 'POST',
        body: { action: 'verify' }
      },
      {
        action: 'full-sync',
        description: 'Import missing Cloudinary assets to database',
        method: 'POST',
        body: { 
          action: 'full-sync',
          options: { limit: 100, folder: 'optional-folder-filter' }
        }
      }
    ]
  })
}
