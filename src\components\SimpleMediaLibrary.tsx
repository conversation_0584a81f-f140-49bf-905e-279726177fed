/**
 * Simple Media Library Component
 * 
 * A clean interface to browse and manage your uploaded media.
 * Shows your media in a grid with search functionality.
 */

'use client'

import { useState, useEffect } from 'react'
import { Search, Trash2, Download, Eye } from 'lucide-react'
import Image from 'next/image'

interface MediaItem {
  id: string
  public_id: string
  secure_url: string
  original_filename: string
  file_size: number
  format: string
  resource_type: string
  width?: number
  height?: number
  created_at: string
}

interface SimpleMediaLibraryProps {
  onSelectMedia?: (media: MediaItem) => void
  selectable?: boolean
  className?: string
}

export default function SimpleMediaLibrary({
  onSelectMedia,
  selectable = false,
  className = ''
}: SimpleMediaLibraryProps) {
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedItem, setSelectedItem] = useState<string | null>(null)

  // Load media items
  const loadMedia = async (query = '') => {
    try {
      setLoading(true)
      const url = query 
        ? `/api/media?q=${encodeURIComponent(query)}&limit=50`
        : '/api/media?limit=50'
      
      const response = await fetch(url)
      const result = await response.json()

      if (result.success) {
        setMediaItems(result.data)
      } else {
        console.error('Failed to load media:', result.error)
      }
    } catch (error) {
      console.error('Error loading media:', error)
    } finally {
      setLoading(false)
    }
  }

  // Search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      loadMedia(searchQuery)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchQuery])

  // Initial load
  useEffect(() => {
    loadMedia()
  }, [])

  const handleDelete = async (publicId: string) => {
    if (!confirm('Are you sure you want to delete this media item?')) {
      return
    }

    try {
      const response = await fetch(`/api/media?public_id=${encodeURIComponent(publicId)}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (result.success) {
        setMediaItems(items => items.filter(item => item.public_id !== publicId))
      } else {
        alert('Failed to delete media item')
      }
    } catch (error) {
      console.error('Delete error:', error)
      alert('Failed to delete media item')
    }
  }

  const handleSelect = (item: MediaItem) => {
    if (selectable) {
      setSelectedItem(item.id)
      onSelectMedia?.(item)
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getMediaPreview = (item: MediaItem) => {
    if (item.resource_type === 'image') {
      return (
        <Image
          src={item.secure_url}
          alt={item.original_filename}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      )
    } else if (item.resource_type === 'video') {
      return (
        <video
          src={item.secure_url}
          className="w-full h-full object-cover"
          muted
        />
      )
    } else {
      return (
        <div className="flex items-center justify-center h-full bg-gray-100">
          <span className="text-2xl font-bold text-gray-400">
            {item.format.toUpperCase()}
          </span>
        </div>
      )
    }
  }

  return (
    <div className={`w-full ${className}`}>
      {/* Search Bar */}
      <div className="mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Search media files..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      )}

      {/* Media Grid */}
      {!loading && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {mediaItems.map((item) => (
            <div
              key={item.id}
              className={`
                group relative bg-white rounded-lg border overflow-hidden cursor-pointer
                transition-all duration-200 hover:shadow-lg
                ${selectable && selectedItem === item.id ? 'ring-2 ring-blue-500' : ''}
              `}
              onClick={() => handleSelect(item)}
            >
              {/* Media Preview */}
              <div className="relative aspect-square">
                {getMediaPreview(item)}
                
                {/* Overlay Actions */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 flex space-x-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        window.open(item.secure_url, '_blank')
                      }}
                      className="p-2 bg-white rounded-full hover:bg-gray-100 transition-colors"
                      title="View"
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        const link = document.createElement('a')
                        link.href = item.secure_url
                        link.download = item.original_filename
                        link.click()
                      }}
                      className="p-2 bg-white rounded-full hover:bg-gray-100 transition-colors"
                      title="Download"
                    >
                      <Download className="h-4 w-4" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleDelete(item.public_id)
                      }}
                      className="p-2 bg-white rounded-full hover:bg-red-100 text-red-600 transition-colors"
                      title="Delete"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Media Info */}
              <div className="p-3">
                <h3 className="text-sm font-medium text-gray-900 truncate" title={item.original_filename}>
                  {item.original_filename}
                </h3>
                <p className="text-xs text-gray-500 mt-1">
                  {formatFileSize(item.file_size)} • {item.format.toUpperCase()}
                </p>
                {item.width && item.height && (
                  <p className="text-xs text-gray-400">
                    {item.width} × {item.height}
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Empty State */}
      {!loading && mediaItems.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">
            {searchQuery ? 'No media found matching your search.' : 'No media files uploaded yet.'}
          </p>
        </div>
      )}
    </div>
  )
}
